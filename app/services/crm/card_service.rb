# frozen_string_literal: true

module Crm
  class CardService
    # Create a new card for a list
    def self.create_card(list_id, params)
      Rails.logger.info("CardService.create_card called with list_id: #{list_id}, params: #{params.inspect}")

      # Use a class variable to track if we're already in the process of creating a card
      # This prevents duplicate card creation from callbacks
      @@creating_card ||= {}

      # If we're already creating a card for this list, return the result
      if @@creating_card[list_id]
        Rails.logger.info("Detected nested card creation for list #{list_id}")
        return @@creating_card[list_id]
      end

      # Mark that we're creating a card for this list (will be updated with actual card later)
      @@creating_card[list_id] = :in_progress

      begin
        # Create the card with standard Rails methods
        list = CrmList.find(list_id)

        # Set position to be at the end if not specified
        params[:position] = list.crm_cards.maximum(:position).to_i + 1 unless params[:position]

        # If patient_id is provided, find the patient and set the title to patient's name
        if params[:patient_id].present?
          # Get the practice_id from the board
          practice_id = list.crm_board.practice_id
          Rails.logger.info("Finding patient with ID: #{params[:patient_id]} for practice: #{practice_id}")

          # Find the patient and ensure they belong to the current practice
          patient = Patient.joins(:practices_patients)
                           .where(id: params[:patient_id], practices_patients: { practice_id: practice_id })
                           .first

          if patient
            params[:title] = patient.full_name
            Rails.logger.info("Found patient: #{patient.full_name}")

            set_last_contacted_from_conversation(patient, params)
          else
            # If patient doesn't belong to this practice, add an error
            Rails.logger.warn("Patient #{params[:patient_id]} does not belong to practice #{practice_id}")
            card = CrmCard.new
            card.errors.add(:base, 'Patient does not belong to this practice')
            return card
          end
        end

        card = list.crm_cards.new(params)

        # Initialize audit trail
        card.audit ||= []
        card.audit << {
          action: 'created',
          time: Time.current.iso8601,
          text: 'Card created'
        }

        # Add patient association info to audit trail if present
        if card.patient_id.present? && card.patient
          card.audit << {
            action: 'associated',
            time: Time.current.iso8601,
            text: "Associated with patient: #{card.patient.full_name}"
          }
        end

        # Save the card
        card.save
        Rails.logger.info("Card saved with ID: #{card.id}")

        # Store the result
        @@creating_card[list_id] = card

        # Return the card
        card
      ensure
        # Clean up class variable after request is complete
        @@creating_card.delete(list_id)
      end
    rescue ActiveRecord::RecordNotFound
      Rails.logger.error("List not found with ID: #{list_id}")
      card = CrmCard.new
      card.errors.add(:base, 'List not found')
      card
    rescue StandardError => e
      Rails.logger.error("Error creating card: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      card = CrmCard.new
      card.errors.add(:base, 'Error creating card')
      card
    end

    # Update a card
    def self.update_card(card_id, params)
      card = CrmCard.find(card_id)

      # Track what changed for audit trail
      old_title = card.title
      old_desc = card.description || ''
      old_labels = card.labels || []
      old_patient_id = card.patient_id

      # If patient_id is provided and changed, find the patient and update the title
      if params[:patient_id].present? && params[:patient_id].to_i != old_patient_id
        patient = Patient.find(params[:patient_id])
        params[:title] = patient.full_name if patient
      end

      # Update the card
      card.update(params)

      # Add audit entries for changes
      card.audit ||= []

      # Check what changed
      changes = []
      changes << "Title updated from \"#{old_title}\" to \"#{card.title}\"" if params[:title] && old_title != card.title
      changes << 'Description updated' if params[:description] && old_desc != card.description
      changes << 'Labels updated' if params[:labels] && old_labels != card.labels

      # Check if patient association changed
      if params[:patient_id].present? && old_patient_id != card.patient_id
        if old_patient_id.present?
          old_patient = Patient.find_by(id: old_patient_id)
          old_patient_name = old_patient ? old_patient.full_name : 'Unknown'
          changes << "Patient association changed from \"#{old_patient_name}\" to \"#{card.patient.full_name}\""
        else
          changes << "Associated with patient: #{card.patient.full_name}"
        end
      elsif params[:patient_id] == '' && old_patient_id.present?
        old_patient = Patient.find_by(id: old_patient_id)
        old_patient_name = old_patient ? old_patient.full_name : 'Unknown'
        changes << "Patient association removed (was: \"#{old_patient_name}\")"
      end

      # Add audit entry if anything changed
      if changes.any?
        card.audit << {
          action: 'edited',
          time: Time.current.iso8601,
          text: changes.join(', ')
        }
        card.save
      end

      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card not found')
      card
    rescue StandardError
      card.errors.add(:base, 'Error updating card')
      card
    end

    # Update a card's treatment
    def self.update_treatment(card_id, treatment_id)
      card = CrmCard.find(card_id)
      card.treatment_id = treatment_id
      if card.save
        treatment = Treatment.find(treatment_id)
        CardActivity.log_activity(
          card: card,
          user: Current.user,
          activity_type: 'updated',
          description: "Treatment changed to '",
          metadata: {
            treatment_id: treatment.id,
            treatment_name: treatment.patient_friendly_name
          }
        )
      end
      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card or Treatment not found')
      card
    end

    # Delete a card
    def self.delete_card(card_id)
      card = CrmCard.find(card_id)
      card.destroy
      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card not found')
      card
    rescue StandardError
      card.errors.add(:base, 'Error deleting card')
      card
    end

    # Move a card to another list
    def self.move_card(card_id, target_list_id, position = nil)
      card = CrmCard.find(card_id)
      source_list = card.crm_list
      target_list = target_list_id.present? ? CrmList.find(target_list_id) : source_list
      can_move = if target_list_id.present?
                   target_list.crm_list_restrictions.all? do |restriction|
                     restriction.check_card(card, source_list)
                   end
                 else
                   true
                 end

      unless can_move
        card.errors.add(:base, 'Card does not meet target list restrictions')
        return card
      end

      begin
        ActiveRecord::Base.transaction do
          # Only add audit entry if moving to a different list
          if source_list.id != target_list.id
            # Add audit entry for the move
            card.audit ||= []
            card.audit << {
              action: 'moved',
              time: Time.current.iso8601,
              text: "Moved from \"#{source_list.title}\" to \"#{target_list.title}\""
            }
          end

          # Update position if provided
          card.position = position if position

          # Update the card's list
          card.crm_list = target_list
          card.save!
        end
      rescue ActiveRecord::RecordInvalid => e
        Rails.logger.error("Error saving card during move: #{e.message}")
        card.errors.add(:base, "Error saving card: #{e.message}")
      rescue StandardError => e
        Rails.logger.error("Error in move_card: #{e.message}")
        card.errors.add(:base, "Error moving card: #{e.message}")
      end

      card
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error("Record not found in move_card: #{e.message}")
      card = CrmCard.new
      card.errors.add(:base, 'Card or list not found')
      card
    rescue StandardError => e
      Rails.logger.error("Unexpected error in move_card: #{e.message}")
      card ||= CrmCard.new
      card.errors.add(:base, "Error moving card: #{e.message}")
      card
    end

    # Move a card to another board
    def self.move_to_board(card_id, target_board_id, target_list_id = nil)
      card = CrmCard.find(card_id)
      source_board = card.crm_list.crm_board
      target_board = CrmBoard.find(target_board_id)

      # Ensure the target board belongs to the same practice as the source board
      unless source_board.practice_id == target_board.practice_id
        card.errors.add(:base, 'Cannot move card to a board from a different practice')
        return card
      end

      # If target list is not specified, use the first list of the target board
      target_list = target_list_id ? CrmList.find(target_list_id) : target_board.crm_lists.first

      can_move = target_list.crm_list_restrictions.all? { |restriction| restriction.check_card(card, card.crm_list) }

      unless can_move
        card.errors.add(:base, 'Card does not meet target list restrictions')
        return card
      end

      ActiveRecord::Base.transaction do
        # Add audit entry for the move
        card.audit ||= []
        card.audit << {
          action: 'moved',
          time: Time.current.iso8601,
          text: "Moved from board \"#{source_board.name}\" to \"#{target_board.name}\""
        }

        # Transfer custom fields
        transfer_service = CustomFieldTransferService.new(source_board, target_board)
        transfer_service.transfer_custom_fields_for_card(card)

        # Update the card's list
        card.crm_list = target_list
        card.save
      rescue StandardError
        card.errors.add(:base, 'Error moving card')
        raise ActiveRecord::Rollback
      end

      card
    rescue ActiveRecord::RecordNotFound
      card = CrmCard.new
      card.errors.add(:base, 'Card, board, or list not found')
      card
    rescue StandardError
      card.errors.add(:base, 'Error moving card to board')
      card
    end
  end

  def self.set_last_contacted_from_conversation(patient, params)
    # Check if the patient has a conversation with messages
    # If so, set the last_contacted_at to the timestamp of the most recent message
    return if patient.linked_conversation.blank?

    conversation = patient.linked_conversation
    last_message = conversation.conversation_messages.order(created_at: :desc).first

    if last_message.present?
      Rails.logger.info("Setting initial last_contacted_at to #{last_message.created_at}")
      params[:last_contacted_at] = last_message.created_at
    end
  end
end
