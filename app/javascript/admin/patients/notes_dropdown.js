// Patient Notes Dropdown
// Handles the dropdown functionality for the Notes button in the patient header

document.addEventListener('DOMContentLoaded', function() {
  initializeNotesDropdown();
});

function initializeNotesDropdown() {
  // Find the dropdown trigger button and menu
  const dropdown = document.querySelector('.notes-dropdown-container');
  const dropdownTrigger = document.querySelector('.notes-dropdown-trigger');
  const dropdownMenu = document.querySelector('.notes-dropdown-menu');
  
  if (!dropdown || !dropdownTrigger || !dropdownMenu) return;
  
  // Check if notes should be expanded initially
  const initiallyExpanded = dropdownTrigger.getAttribute('aria-expanded') === 'true';
  const patientId = getPatientId();
  
  // Position the dropdown first (while still hidden)
  positionDropdown(dropdownMenu, dropdownTrigger);
  
  // If it should be expanded initially and not muted, show the dropdown
  if (initiallyExpanded && patientId && !isNotesMuted(patientId)) {
    // Add a delay to ensure this runs after any conflicting JavaScript on the charting page
    setTimeout(() => {
      // Double-check that the dropdown should still be expanded (in case something changed)
      if (dropdownTrigger.getAttribute('aria-expanded') === 'true') {
        dropdownMenu.classList.remove('hidden');
        document.addEventListener('click', closeDropdownOnClickOutside);

        // Mark the dropdown as auto-opened to prevent immediate closure
        dropdownMenu.setAttribute('data-auto-opened', 'true');
        setTimeout(() => {
          dropdownMenu.removeAttribute('data-auto-opened');
        }, 500); // Remove the protection after 500ms
      }
    }, 200); // 200ms delay to ensure all other scripts have loaded
  } else if (initiallyExpanded) {
    // Notes are muted, so don't show them but keep aria-expanded consistent with the DOM
    dropdownTrigger.setAttribute('aria-expanded', 'false');
  }
  
  // Toggle dropdown when clicking the trigger button
  dropdownTrigger.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();

    const isExpanded = dropdownTrigger.getAttribute('aria-expanded') === 'true';

    // Toggle dropdown state
    dropdownTrigger.setAttribute('aria-expanded', !isExpanded);

    if (!isExpanded) {
      showDropdown(dropdownMenu, dropdownTrigger);
      document.addEventListener('click', closeDropdownOnClickOutside);
    } else {
      hideDropdown(dropdownMenu);
      document.removeEventListener('click', closeDropdownOnClickOutside);
    }
  });

  // Prevent clicks inside the dropdown from closing it
  dropdownMenu.addEventListener('click', function(e) {
    e.stopPropagation();
  });
  
  // Close dropdown when clicking outside
  function closeDropdownOnClickOutside(e) {
    // Don't close if the dropdown was just auto-opened (protection period)
    if (dropdownMenu.getAttribute('data-auto-opened') === 'true') {
      return;
    }

    if (!dropdown.contains(e.target)) {
      hideDropdown(dropdownMenu);
      dropdownTrigger.setAttribute('aria-expanded', 'false');
      document.removeEventListener('click', closeDropdownOnClickOutside);
    }
  }
  
  // Close dropdown when pressing Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && dropdownTrigger.getAttribute('aria-expanded') === 'true') {
      hideDropdown(dropdownMenu);
      dropdownTrigger.setAttribute('aria-expanded', 'false');
      document.removeEventListener('click', closeDropdownOnClickOutside);
    }
  });
}

function positionDropdown(menu, trigger) {
  // Position the dropdown directly under the trigger button
  const triggerRect = trigger.getBoundingClientRect();
  
  // Set fixed position
  menu.style.position = 'fixed';
  menu.style.top = (triggerRect.bottom + 10) + 'px'; // Position below trigger with 10px gap
  menu.style.right = (window.innerWidth - triggerRect.right) + 'px';
  menu.style.zIndex = '20'; // Set to a lower z-index as requested
}

function showDropdown(menu, trigger) {
  // First position the dropdown
  positionDropdown(menu, trigger);
  
  // Then show the menu
  menu.classList.remove('hidden');
}

function hideDropdown(menu) {
  menu.classList.add('hidden');
}

function getPatientId() {
  // Try to get patient ID from URL
  const urlMatch = window.location.pathname.match(/\/admin\/patients\/(\d+)/);
  if (urlMatch && urlMatch[1]) {
    return urlMatch[1];
  }
  
  // If not found in URL, try to get from a data attribute on the page
  const patientContainer = document.querySelector('[data-patient-id]');
  if (patientContainer) {
    return patientContainer.getAttribute('data-patient-id');
  }
  
  return null;
}

function isNotesMuted(patientId) {
  // Check if window.patientNotesMute exists (from mute_notes.js)
  if (window.patientNotesMute && window.patientNotesMute.areNotesMuted) {
    return window.patientNotesMute.areNotesMuted(patientId);
  }
  
  // Fallback implementation if mute_notes.js isn't loaded yet
  const muteData = localStorage.getItem(`muted_notes_${patientId}`);
  
  if (!muteData) return false;
  
  try {
    const data = JSON.parse(muteData);
    
    // If mute option is "next-select", it's always muted until explicitly cleared
    if (data.expiryTime === 'next-select') {
      return true;
    }
    
    // Check if the mute has expired
    const now = new Date().getTime();
    return data.muted && now < data.expiryTime;
  } catch (e) {
    return false;
  }
}
