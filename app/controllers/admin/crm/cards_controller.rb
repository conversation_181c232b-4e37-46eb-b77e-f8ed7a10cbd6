# frozen_string_literal: true

module Admin
  module Crm
    class CardsController < Admin::ApplicationController
      include Admin::ConversationsHelper
      include Admin::PatientAssetsHelper

      before_action :set_list, except: %i[
        move move_to_board edit show conversation directions assets payments actions labels
        update_last_contacted update_description update_status index completed archived
        add_checklist remove_checklist add_team_member remove_team_member appointments archive
      ]
      before_action :set_card, only: %i[
        update destroy edit show conversation directions assets payments actions labels
        update_last_contacted update_description update_status restore add_checklist
        remove_checklist add_team_member remove_team_member appointments archive
      ]

      def create
        Rails.logger.info("CardsController#create called with params: #{params.inspect}")
        Rails.logger.info("Card params: #{card_params.inspect}")

        # Add detailed logging to track the request
        Rails.logger.info("Request headers: #{request.headers.env.select { |k, _v| k.start_with?('HTTP_') }.inspect}")
        Rails.logger.info("Request method: #{request.method}")
        Rails.logger.info("Request format: #{request.format}")

        # byebug - removed to prevent hanging
        @card = ::Crm::CardService.create_card(@list.id, card_params)

        respond_to do |format|
          if @card.persisted?
            format.html { redirect_to admin_crm_board_path(@list.crm_board), notice: 'Card was successfully created.' }
            format.json { render json: successful_json_response }
          else
            format.html { redirect_to admin_crm_board_path(@list.crm_board), alert: @card.errors.full_messages.join(', ') }
            format.json { render json: { success: false, errors: @card.errors.full_messages } }
          end
        end
      end

      def index
        @board = CrmBoard.find(params[:board_id]) if params[:board_id].present?
        @list = CrmList.find(params[:list_id]) if params[:list_id].present?

        if params[:status].present?
          # Filter cards by status
          @cards = CrmCard.where(status: params[:status])

          render json: {
            success: true,
            cards: @cards.map do |card|
              {
                id: card.id,
                title: card.title,
                patient_name: card.patient&.full_name,
                completed_at: card.completed_at&.strftime('%b %d, %Y %I:%M %p')
              }
            end
          }
          return
        end

        # Regular index action for active cards
        @cards = if @list
                   @list.crm_cards.active.order(:position)
                 elsif @board
                   @board.crm_cards.active.includes(:crm_list).order(:position)
                 else
                   CrmCard.active.includes(:crm_list).order(:position)
                 end

        respond_to do |format|
          format.html
          format.json { render json: @cards }
        end
      end

      def update
        @card = ::Crm::CardService.update_card(@card.id, card_params)

        respond_to do |format|
          if @card.errors.empty?
            format.html { redirect_after_successful_update }
            format.json { render json: successful_json_response }
          else
            format.html { redirect_to admin_crm_board_path(@list.crm_board), alert: @card.errors.full_messages.join(', ') }
            format.json { render json: { success: false, errors: @card.errors.full_messages } }
          end
        end
      end

      def destroy
        ::Crm::CardService.delete_card(@card.id)

        respond_to do |format|
          format.html { redirect_to admin_crm_board_path(@list.crm_board), notice: 'Card was successfully deleted.' }
          format.json { render json: { success: true } }
        end
      end

      def move
        @card = CrmCard.find(params[:id])

        # If treatment_id is provided, update the card's treatment
        if params[:treatment_id].present?
          @card = ::Crm::CardService.update_treatment(@card.id, params[:treatment_id])

          respond_to do |format|
            if @card.errors.empty?
              format.html { redirect_to admin_crm_card_path(@card), notice: 'Treatment was successfully updated.' }
              format.json { render json: { success: true, card: @card } }
            else
              format.html { redirect_to admin_crm_card_path(@card), alert: @card.errors.full_messages.join(', ') }
              format.json { render json: { success: false, errors: @card.errors.full_messages } }
            end
          end
        else
          # If no treatment_id, proceed with the original list move logic
          Rails.logger.info("Moving card #{params[:id]} to list #{params[:list_id]} at position #{params[:position]}")

          @card = ::Crm::CardService.move_card(
            params[:id],
            params[:list_id],
            params[:position]
          )

          respond_to do |format|
            if @card.errors.empty?
              format.html { redirect_to admin_crm_board_path(@card.crm_list.crm_board), notice: 'Card was successfully moved.' }
              format.json { render json: { success: true, card: @card } }
            else
              Rails.logger.error("Error moving card: #{@card.errors.full_messages.join(', ')}")
              format.html { redirect_to admin_crm_board_path(@card.crm_list.crm_board), alert: @card.errors.full_messages.join(', ') }
              format.json { render json: { success: false, errors: @card.errors.full_messages } }
            end
          end
        end
      rescue StandardError => e
        Rails.logger.error("Exception in move action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))

        respond_to do |format|
          format.html { redirect_to admin_crm_boards_path, alert: "An error occurred: #{e.message}" }
          format.json { render json: { success: false, errors: ["An error occurred: #{e.message}"] }, status: :unprocessable_entity }
        end
      end

      def move_to_board
        @card = ::Crm::CardService.move_to_board(
          params[:id],
          params[:board_id],
          params[:list_id]
        )

        respond_to do |format|
          if @card.errors.empty?
            format.html { redirect_to admin_crm_board_path(params[:board_id]), notice: 'Card was successfully moved.' }
            format.json { render json: { success: true, card: @card } }
          else
            format.html { redirect_back(fallback_location: admin_crm_boards_path, alert: @card.errors.full_messages.join(', ')) }
            format.json { render json: { success: false, errors: @card.errors.full_messages } }
          end
        end
      end

      def edit
        respond_to do |format|
          format.html { render :edit }
          format.json { render json: card_with_audit_json_response }
        end
      rescue ActiveRecord::RecordNotFound
        respond_to do |format|
          format.html { redirect_to admin_crm_boards_path, alert: 'Card not found.' }
          format.json { render json: { success: false, error: 'Card not found' }, status: :not_found }
        end
      end

      def show
        # Eager load board to reduce queries
        @board = @card.crm_list.crm_board

        # Limit and cache treatments query
        @treatments = Treatment.select(:id, :patient_friendly_name).order(:patient_friendly_name)

        # Optimize checklist queries
        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.select(:id, :title)

        # Optimize team member queries with preloaded user data
        user_ids = @card.members.present? ? @card.members.map { |m| m['user_id'] }.compact : []
        @card_team_member_ids = user_ids
        @all_team_members = User.select(:id, :first_name, :last_name).includes(image_attachment: :blob).order(:first_name, :last_name)
        @all_team_members = User.select(:id, :first_name, :last_name).order(:first_name, :last_name)

        # Get custom fields for this board with eager loading
        @custom_fields = @board.crm_custom_fields.order(position: :asc)

        # Optimize custom field values with a single query
        field_values = @card.crm_custom_field_values.to_a
        @custom_field_values = field_values.each_with_object({}) do |field_value, hash|
          hash[field_value.crm_custom_field_id] = field_value.value
        end

        # Preload card activities and comments to avoid N+1 queries
        @card_activities = @card.card_activities.includes(user: { image_attachment: :blob })
        @card_comments = @card.card_comments.includes(user: { image_attachment: :blob }).where(ancestry: nil)

        respond_to do |format|
          format.html
          format.json { render json: card_with_audit_json_response }
        end
      rescue ActiveRecord::RecordNotFound
        respond_to do |format|
          format.html { redirect_to admin_crm_boards_path, alert: 'Card not found.' }
          format.json { render json: { success: false, error: 'Card not found' }, status: :not_found }
        end
      end

      def conversation
        @board = @card.crm_list.crm_board
        # Optimize checklist queries
        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.select(:id, :title)

        # Optimize team member queries with select
        @card_team_member_ids = @card.member_users.pluck(:id)
        @all_team_members = User.select(:id, :first_name, :last_name).order(:first_name, :last_name)
        if @card.patient.present?
          @conversation = @card.patient.linked_conversation
          @messages = @conversation.conversation_messages
          @messages = @messages.where(label: params[:message_filter]).order(created_at: :asc) if params[:message_filter].present?
          if params[:filter].present?
            @messages = @messages.where(message_type: 'outbound',
                                        from: params[:filter]).or(@messages.where.not(message_type: 'outbound'))
          end

          @messages = @messages.order(created_at: :asc)

          # Update the last_contacted_at field if there are messages
          if @messages.any? && (@card.last_contacted_at.nil? || @card.last_contacted_at < @messages.last.created_at)
            Rails.logger.info("Updating last_contacted_at for card #{@card.id} to #{@messages.last.created_at}")
            @card.update(last_contacted_at: @messages.last.created_at)
          end

          @whatsapp_templates = load_whatsapp_templates if defined?(load_whatsapp_templates)
        end

        respond_to do |format|
          format.html
          format.json do
            render json: {
              success: true,
              card: @card.as_json,
              conversation: @conversation&.as_json
            }
          end
        end
      end

      def directions
        @board = @card.crm_list.crm_board

        # Get all checklists and the ones associated with this card for the sidebar
        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.all

        # Get all team members and the ones associated with this card for the sidebar
        @card_team_member_ids = @card.member_users.pluck(:id)
        @all_team_members = User.all.order(:first_name, :last_name)

        # Get all practices for the destination dropdown
        @practices = Practice.all.order(:name)

        respond_to do |format|
          format.html
          format.json do
            render json: {
              success: true,
              card: @card.as_json
            }
          end
        end
      end

      def appointments
        @board = @card.crm_list.crm_board

        # Get all checklists and the ones associated with this card for the sidebar
        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.all

        # Get all team members and the ones associated with this card for the sidebar
        @card_team_member_ids = @card.member_users.pluck(:id)
        @all_team_members = User.all.order(:first_name, :last_name)

        respond_to do |format|
          format.html
          format.json do
            render json: {
              success: true,
              card: @card.as_json
            }
          end
        end
      end

      def assets
        @board = @card.crm_list.crm_board

        if @card.patient.present?
          @assets = @card.patient.patient_assets.order('created_at DESC')
          @labels = [*Admin::PatientAssetsHelper::DEFAULT_LABELS, *PatientAssetLabel.pluck(:label)].uniq
        end

        # Get all checklists and the ones associated with this card for the sidebar
        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.all

        # Get all team members and the ones associated with this card for the sidebar
        @card_team_member_ids = @card.member_users.pluck(:id)
        @all_team_members = User.all.order(:first_name, :last_name)

        respond_to do |format|
          format.html
          format.json do
            render json: {
              success: true,
              card: @card.as_json,
              assets: @assets&.as_json
            }
          end
        end
      end

      def payments
        @board = @card.crm_list.crm_board

        # Get all checklists and the ones associated with this card for the sidebar
        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.all

        # Get all team members and the ones associated with this card for the sidebar
        @card_team_member_ids = @card.member_users.pluck(:id)
        @all_team_members = User.all.order(:first_name, :last_name)

        # Load patient payments and invoices if patient exists
        if @card.patient.present?
          @payments = @card.patient.payments.order(created_at: :desc)
          @invoices = @card.patient.invoices.order(created_at: :desc)
        else
          @payments = []
          @invoices = []
        end

        respond_to do |format|
          format.html
          format.json do
            render json: {
              success: true,
              card: @card.as_json,
              payments: @payments.as_json
            }
          end
        end
      end

      def actions
        @board = @card.crm_list.crm_board

        # Get all checklists and the ones associated with this card for the sidebar
        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.all

        # Get all team members and the ones associated with this card for the sidebar
        @card_team_member_ids = @card.member_users.pluck(:id)
        @all_team_members = User.all.order(:first_name, :last_name)

        # Get the patient's actions and card's actions
        @patient = @card.patient
        @actions = []

        # Add patient actions if patient exists
        if @patient.present?
          Rails.logger.debug "Loading actions for patient: #{@patient.id}"
          patient_actions = Action.where(actionable: @patient)
                                  .or(Action.where('actionable_type = ? AND actionable_id IN (?)',
                                                   'CalendarBooking',
                                                   @patient.calendar_bookings.pluck(:id)))
          Rails.logger.debug "Found #{patient_actions.count} patient actions"
          @actions.concat(patient_actions)
        end

        # Add card actions
        Rails.logger.debug "Loading actions for card: #{@card.id}"
        card_actions = Action.where(actionable: @card)
        Rails.logger.debug "Found #{card_actions.count} card actions"
        @actions.concat(card_actions)

        # Add all actions for debugging
        Rails.logger.debug "Total actions in system: #{Action.count}"
        Rails.logger.debug "Actions by type: #{Action.group(:action_type).count}"

        # Sort all actions
        @actions = @actions.sort_by { |a| [a.date_due || Time.zone.now + 100.years, a.created_at] }

        # Group actions by type (filter the array since @actions is now an Array, not an ActiveRecord relation)
        @tasks = @actions.select { |a| a.action_type == 'task' }
        @reminders = @actions.select { |a| a.action_type == 'reminder' }
        @alerts = @actions.select { |a| a.action_type == 'alert' }
        @callbacks = @actions.select { |a| a.action_type == 'callback' }
        @comments = @actions.select { |a| a.action_type == 'comment' }
        @complaints = @actions.select { |a| a.action_type == 'complaint' }

        # Debug info
        Rails.logger.debug 'Action counts by type:'
        Rails.logger.debug "Tasks: #{@tasks.count}"
        Rails.logger.debug "Reminders: #{@reminders.count}"
        Rails.logger.debug "Alerts: #{@alerts.count}"
        Rails.logger.debug "Callbacks: #{@callbacks.count}"
        Rails.logger.debug "Comments: #{@comments.count}"
        Rails.logger.debug "Complaints: #{@complaints.count}"
      end

      def labels
        @board = @card.crm_list.crm_board
        labels_data = @board.available_labels

        render json: {
          success: true,
          card_labels: @card.crm_labels,
          board_labels: labels_data[:label_objects]
          # Legacy labels removed
        }
      end

      # This method is used by the CardLabelsController, not directly in this controller
      # The route is defined as resource :card_labels, only: [:update]
      # See Admin::Crm::CardLabelsController

      def board_lists
        @board = CrmBoard.find(params[:board_id])
        render json: {
          success: true,
          lists: @board.crm_lists.as_json(only: %i[id title])
        }
      rescue ActiveRecord::RecordNotFound
        render json: { success: false, error: 'Board not found' }, status: :not_found
      end

      def completed
        @board = CrmBoard.find(params[:board_id])
        @cards = @board.completed_cards.includes(:patient).order(completed_at: :desc)
        @status_type = 'complete'
        @title = 'Completed Cards'

        render :status_cards
      end

      def archived
        @board = CrmBoard.find(params[:board_id])
        @cards = @board.archived_cards.includes(:patient).order(completed_at: :desc)
        @status_type = 'archive'
        @title = 'Archived Cards'

        render :status_cards
      end

      def update_last_contacted
        # Handle both date and datetime-local formats
        datetime = if params[:last_contacted_at].present?
                     # Handle datetime-local format from HTML5 input (YYYY-MM-DDThh:mm)
                     DateTime.parse(params[:last_contacted_at])
                   elsif params[:date].present?
                     # Legacy support for date parameter
                     Date.parse(params[:date])
                   else
                     Time.current
                   end

        if @card.update(last_contacted_at: datetime)
          # Log activity for the last contacted date change
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Updated last contacted date to #{@card.formatted_last_contacted}"
          )

          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), notice: 'Last contacted date updated.') }
            format.json do
              # Use the precise_time_ago_in_words helper for consistent formatting
              time_ago = view_context.precise_time_ago_in_words(@card.last_contacted_at, include_seconds: true)
              render json: {
                success: true,
                last_contacted_at: @card.last_contacted_at,
                formatted_date: @card.formatted_last_contacted,
                time_ago: time_ago
              }
            end
          end
        else
          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), alert: 'Failed to update last contacted date.') }
            format.json { render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity }
          end
        end
      end

      def update_description
        if @card.update(description: params[:description])
          # Log activity for the description update
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: 'Updated card description'
          )

          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), notice: 'Description updated.') }
            format.json { render json: { success: true } }
          end
        else
          respond_to do |format|
            format.html { redirect_back(fallback_location: admin_crm_card_path(@card), alert: 'Failed to update description.') }
            format.json { render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity }
          end
        end
      end

      def update_status
        status = params[:status]

        # Log the parameters for debugging
        Rails.logger.info("Updating card status: #{@card.id} to #{status}")

        # Get all valid statuses - built-in ones plus custom completion zones
        board = @card.crm_list.crm_board
        custom_statuses = board.crm_completion_zones.pluck(:name).map(&:downcase)
        valid_statuses = %w[complete archive] + custom_statuses

        # Update the card status if it's valid
        if valid_statuses.include?(status)
          # Update the card status directly
          if @card.update(status: status, completed_at: Time.current)
            Rails.logger.info('Card status updated successfully')

            # Log activity for the status change
            CardActivity.log_activity(
              card: @card,
              user: Current.user,
              activity_type: 'updated',
              description: "Card marked as #{status}"
            )

            render json: { success: true, card: @card.as_json }
          else
            Rails.logger.error("Failed to update card status: #{@card.errors.full_messages}")
            render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity
          end
        else
          Rails.logger.error("Invalid status: #{status}")
          render json: { success: false, errors: ["Invalid status: #{status}"] }, status: :unprocessable_entity
        end
      end

      def restore
        @card = CrmCard.find(params[:id])

        if @card.update(status: 'active')
          # Log activity for the restore
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Card restored from #{@card.status == 'complete' ? 'completed' : 'archived'} status"
          )

          render json: { success: true, card: @card.as_json }
        else
          render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity
        end
      rescue StandardError => e
        Rails.logger.error("Exception in restore action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        render json: { success: false, errors: ["Error restoring card: #{e.message}"] }, status: :internal_server_error
      end

      def archive
        Rails.logger.info("Archiving card: #{@card.id}")

        if @card.update(status: 'archive', completed_at: Time.current)
          # Log activity for the archive action
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'archived',
            description: 'Card archived'
          )

          render json: { success: true, card: @card.as_json }
        else
          Rails.logger.error("Failed to archive card: #{@card.errors.full_messages}")
          render json: { success: false, errors: @card.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def add_checklist
        begin
          Rails.logger.info("Add checklist params: #{params.inspect}")

          @card = CrmCard.find(params[:id])
          Rails.logger.info("Found card: #{@card.inspect}")

          # Extract and clean the checklist_id parameter
          raw_checklist_id = params[:checklist_id]
          # Remove any quotes that might be present
          checklist_id_clean = raw_checklist_id.to_s.gsub(/"/, '')
          # Convert to integer
          checklist_id = checklist_id_clean.to_i
          Rails.logger.info("Raw checklist_id: #{raw_checklist_id}, Cleaned: #{checklist_id_clean}, Final ID: #{checklist_id}")

          # Check if the checklist exists
          checklist = CrmChecklist.find_by(id: checklist_id)

          if checklist.nil?
            Rails.logger.error("Checklist not found with ID: #{checklist_id}")
            render json: { success: false, error: "Checklist not found with ID: #{checklist_id}" }, status: :not_found
            return
          end

          Rails.logger.info("Found checklist: #{checklist.inspect}")

          # Check if the checklist is already associated with the card through the new relationship
          Rails.logger.info('Checking if checklist is already associated with card...')
          Rails.logger.info("Card checklists: #{@card.crm_checklists.pluck(:id)}")

          # Detailed debugging for the association check
          has_many_association = @card.crm_checklists.include?(checklist)
          Rails.logger.info("Has many association check: #{has_many_association}")

          # Check the join table directly with a more detailed query
          join_table_query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id} AND crm_checklist_id = #{checklist.id}"
          Rails.logger.info("Join table query: #{join_table_query}")

          join_table_results = ActiveRecord::Base.connection.execute(join_table_query).to_a
          Rails.logger.info("Join table results: #{join_table_results.inspect}")

          join_exists = join_table_results.any?
          Rails.logger.info("Join table entry exists: #{join_exists}")

          # Also check the old relationship
          old_relationship = checklist.crm_card_id == @card.id
          Rails.logger.info("Old relationship check: #{old_relationship}, checklist.crm_card_id: #{checklist.crm_card_id}")

          if has_many_association || join_exists
            Rails.logger.info('Checklist already added to card through has_and_belongs_to_many')
            Rails.logger.info("Association details: has_many_association=#{has_many_association}, join_exists=#{join_exists}")
            render json: { success: false, error: 'Checklist is already added to this card' }, status: :unprocessable_entity
            return
          end

          # Check if the checklist is associated through the old relationship (has crm_card_id)
          if checklist.crm_card_id == @card.id
            Rails.logger.info('Checklist already added to card through old has_many relationship')
            # Instead of showing an error, we'll migrate it to the new relationship
            Rails.logger.info('Migrating checklist from old to new relationship')
            # Clear the old association
            checklist.crm_card_id = nil
            checklist.save
          end

          # Associate the checklist with the card
          begin
            Rails.logger.info("Before association - Card checklists: #{@card.crm_checklists.pluck(:id)}")
            query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id}"
            result = ActiveRecord::Base.connection.execute(query).to_a
            Rails.logger.info("Before association - Join table entries for card: #{result}")

            # First, do one final check to see if the association already exists
            final_check_query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id} AND crm_checklist_id = #{checklist.id}"
            final_check_results = ActiveRecord::Base.connection.execute(final_check_query).to_a

            if final_check_results.any?
              # The association already exists, but wasn't detected earlier
              # This is likely due to a race condition or caching issue
              Rails.logger.info('Final check shows association already exists!')
              Rails.logger.info("Final check results: #{final_check_results.inspect}")

              # Return success instead of error since the checklist is already associated
              @card.reload # Make sure the card has the latest associations

              # Log activity for adding the checklist
              CardActivity.log_activity(
                card: @card,
                user: Current.user,
                activity_type: 'updated',
                description: "Added checklist: #{checklist.title}"
              )

              render json: { success: true, message: 'Checklist was already associated with this card' }
              return
            end

            # Try using a direct SQL insert instead of the association method
            insert_sql = "INSERT INTO crm_cards_checklists (crm_card_id, crm_checklist_id) VALUES (#{@card.id}, #{checklist.id})"
            Rails.logger.info("Executing SQL: #{insert_sql}")

            begin
              ActiveRecord::Base.connection.execute(insert_sql)
              Rails.logger.info('SQL insert successful')
            rescue ActiveRecord::StatementInvalid => e
              # Check if this is a unique constraint violation
              if e.message.include?('PG::UniqueViolation') || e.message.include?('already exists')
                Rails.logger.info('Unique constraint violation - association already exists')

                # Return success instead of error since the checklist is already associated
                @card.reload # Make sure the card has the latest associations

                # Log activity for adding the checklist
                CardActivity.log_activity(
                  card: @card,
                  user: Current.user,
                  activity_type: 'updated',
                  description: "Added checklist: #{checklist.title}"
                )

                render json: { success: true, message: 'Checklist was already associated with this card' }
                return
              else
                # Some other SQL error occurred
                Rails.logger.error("SQL insert failed: #{e.message}")
                # Try the association method as a fallback
                begin
                  @card.crm_checklists << checklist
                  Rails.logger.info('Associated checklist with card using association method')
                rescue StandardError => assoc_error
                  Rails.logger.error("Association method also failed: #{assoc_error.message}")
                  raise assoc_error # Re-raise to be caught by the outer begin/rescue
                end
              end
            end

            # Verify the association was created
            @card.reload
            join_table_query = "SELECT * FROM crm_cards_checklists WHERE crm_card_id = #{@card.id} AND crm_checklist_id = #{checklist.id}"
            join_table_results = ActiveRecord::Base.connection.execute(join_table_query).to_a
            join_exists_after = join_table_results.any?

            Rails.logger.info("After association - Join table query: #{join_table_query}")
            Rails.logger.info("After association - Join table results: #{join_table_results.inspect}")
            Rails.logger.info("After association - Join table entry exists: #{join_exists_after}")
            Rails.logger.info("After association - Card checklists: #{@card.crm_checklists.pluck(:id)}")

            # Double check the association
            has_many_after = @card.crm_checklists.include?(checklist)
            Rails.logger.info("After association - Has many association check: #{has_many_after}")
          rescue StandardError => e
            Rails.logger.error("Error associating checklist with card: #{e.message}")
            Rails.logger.error(e.backtrace.join("\n"))
            raise e
          end

          # Log activity for adding the checklist
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Added checklist: #{checklist.title}"
          )

          render json: { success: true, message: 'Checklist added successfully' }
        rescue ActiveRecord::RecordNotFound => e
          Rails.logger.error("RecordNotFound error: #{e.message}")
          render json: { success: false, error: "Error adding checklist: #{e.message}" }, status: :not_found
        rescue StandardError => e
          Rails.logger.error("General error in add_checklist: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))
          render json: { success: false, error: "Error adding checklist: #{e.message}" }, status: :unprocessable_entity
        end
      rescue StandardError => e
        Rails.logger.error("Exception in add_checklist action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        render json: { success: false, error: "Error adding checklist: #{e.message}" }, status: :internal_server_error
      end

      def remove_checklist
        @card = CrmCard.find(params[:id])
        checklist = CrmChecklist.find(params[:checklist_id])

        # Check if the checklist is associated with the card
        unless @card.crm_checklists.include?(checklist)
          render json: { success: false, error: 'Checklist is not associated with this card' }, status: :unprocessable_entity
          return
        end

        # Remove the association between the card and the checklist
        @card.crm_checklists.delete(checklist)

        # Log activity for removing the checklist
        CardActivity.log_activity(
          card: @card,
          user: Current.user,
          activity_type: 'updated',
          description: "Removed checklist: #{checklist.title}"
        )

        render json: { success: true, message: 'Checklist removed successfully' }
      rescue StandardError => e
        Rails.logger.error("Exception in remove_checklist action: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        render json: { success: false, error: "Error removing checklist: #{e.message}" }, status: :internal_server_error
      end

      def add_team_member
        member_id = params[:member_id]
        user = User.find(member_id)

        # Check if the member is already added to the card
        if @card.member_users.include?(user)
          render json: { success: false, error: 'Team member is already assigned to this card.' }
          return
        end

        # Initialize members array if it doesn't exist
        @card.members ||= []

        # Add the member to the card's members JSON field
        @card.members << { 'user_id' => user.id }

        # Save the card with the updated members
        if @card.save
          # Log the activity
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Added #{user.full_name} to the team",
            metadata: { action: 'add_team_member', member_id: member_id }
          )

          # Return the updated team members list for UI update
          render json: {
            success: true,
            message: 'Team member added successfully',
            team_members: @card.member_users.map do |u|
                            {
                              id: u.id,
                              first_name: u.first_name,
                              last_name: u.last_name,
                              full_name: u.full_name
                            }
                          end
          }
        else
          render json: { success: false, error: @card.errors.full_messages.join(', ') }
        end
      rescue ActiveRecord::RecordNotFound
        render json: { success: false, error: 'Team member not found' }, status: :not_found
      end

      def remove_team_member
        member_id = params[:member_id]
        user = User.find(member_id)

        # Check if the member is actually added to the card
        unless @card.member_users.include?(user)
          render json: { success: false, error: 'Team member is not assigned to this card.' }
          return
        end

        # Initialize members array if it doesn't exist
        @card.members ||= []

        # Remove the member from the card's members JSON field
        @card.members.reject! { |m| m['user_id'].to_s == member_id.to_s }

        # Save the card with the updated members
        if @card.save
          # Log the activity
          CardActivity.log_activity(
            card: @card,
            user: Current.user,
            activity_type: 'updated',
            description: "Removed #{user.full_name} from the team",
            metadata: { action: 'remove_team_member', member_id: member_id }
          )

          # Return the updated team members list for UI update
          render json: {
            success: true,
            message: 'Team member removed successfully',
            team_members: @card.member_users.map do |u|
                            {
                              id: u.id,
                              first_name: u.first_name,
                              last_name: u.last_name,
                              full_name: u.full_name
                            }
                          end
          }
        else
          render json: { success: false, error: @card.errors.full_messages.join(', ') }
        end
      rescue ActiveRecord::RecordNotFound
        render json: { success: false, error: 'Team member not found' }, status: :not_found
      end

      private

      def set_list
        @list = CrmList.find(params[:list_id])
      end

      def set_card
        @card = if @list
                  @list.crm_cards.includes(
                    :patient,
                    { crm_list: :crm_board },
                    :crm_labels,
                    :crm_custom_field_values
                  ).find(params[:id])
                else
                  CrmCard.includes(
                    :patient,
                    { crm_list: :crm_board },
                    :crm_labels,
                    :crm_custom_field_values
                  ).find(params[:id])
                end
      end

      def card_params
        params.require(:crm_card).permit(
          :title,
          :description,
          :position,
          :patient_id,
          :practice_id,
          :treatment_id,
          :last_contacted_at,
          labels: [],
          crm_custom_field_values_attributes: %i[id crm_custom_field_id value]
        )
      end

      def redirect_after_successful_update
        if request.referer&.include?(admin_crm_card_path(@card))
          redirect_to admin_crm_card_path(@card), notice: 'Card was successfully updated.'
        else
          redirect_to admin_crm_board_path(@list&.crm_board || @card.crm_list.crm_board), notice: 'Card was successfully updated.'
        end
      end

      def successful_json_response
        card_json = prepare_card_json

        {
          success: true,
          html: render_to_string(partial: 'admin/crm/cards/card', formats: [:html], locals: { card: @card }),
          card_id: @card.id,
          card: card_json
        }
      end

      def card_with_audit_json_response
        card_json = prepare_card_json

        # Include audit trail
        card_json['audit'] = @card.audit if @card.respond_to?(:audit)

        {
          success: true,
          card: card_json
        }
      end

      def prepare_card_json
        card_json = @card.as_json
        if @card.patient.present?
          card_json['patient'] = {
            id: @card.patient.id,
            first_name: @card.patient.first_name,
            last_name: @card.patient.last_name,
            email: @card.patient.email,
            mobile_phone: @card.patient.mobile_phone,
            full_name: @card.patient.full_name
          }
        end
        card_json
      end
    end
  end
end
